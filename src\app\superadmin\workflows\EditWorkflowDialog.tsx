"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Workflow } from "../../../../convex/workflows";

interface WorkflowFormData {
  title: string;
  description: string;
}

interface EditWorkflowDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  workflow: Workflow | null;
  onSubmit: (data: WorkflowFormData) => Promise<void>;
}

export function EditWorkflowDialog({ isOpen, onOpenChange, workflow, onSubmit }: EditWorkflowDialogProps) {
  const editForm = useForm<WorkflowFormData>({
    defaultValues: {
      title: workflow?.title || "",
      description: workflow?.description || "",
    },
  });

  // Reset form when workflow changes
  React.useEffect(() => {
    if (workflow) {
      editForm.reset({
        title: workflow.title,
        description: workflow.description || "",
      });
    }
  }, [workflow, editForm]);

  const handleSubmit = async (data: WorkflowFormData) => {
    await onSubmit(data);
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Workflow</DialogTitle>
          <DialogDescription>Make changes to the workflow details below.</DialogDescription>
        </DialogHeader>
        <Form {...editForm}>
          <form onSubmit={editForm.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={editForm.control}
              name="title"
              rules={{ required: "Title is required" }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter workflow title" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={editForm.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter workflow description" className="min-h-[100px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={editForm.formState.isSubmitting}>
                {editForm.formState.isSubmitting ? "Saving..." : "Save Changes"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
