import { Id } from "./_generated/dataModel";
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export interface RunningWorkflow {
  _id: Id<"running_workflows">;
  title: string;
  description?: string;
  userId: string;
  createdAt: string;
  running_tasks?: Id<"running_tasks">[];
}

// Get all running workflows
export const getRunningWorkflows = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("running_workflows").collect();
  },
});

// Get a single running workflow by ID
export const getRunningWorkflow = query({
  args: { id: v.id("running_workflows") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Get running workflows by user
export const getRunningWorkflowsByUser = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("running_workflows")
      .withIndex("byUserId", (q) => q.eq("userId", args.userId))
      .collect();
  },
});

// Create a new running workflow
export const createRunningWorkflow = mutation({
  args: {
    title: v.string(),
    description: v.optional(v.string()),
    running_tasks: v.optional(v.array(v.id("running_tasks"))),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    const userId = user?.subject;
    if (!userId) throw new Error("Not authenticated");

    return await ctx.db.insert("running_workflows", {
      title: args.title,
      description: args.description,
      userId,
      createdAt: new Date().toISOString(),
      running_tasks: args.running_tasks ?? [],
    });
  },
});

// Update an existing running workflow
export const updateRunningWorkflow = mutation({
  args: {
    id: v.id("running_workflows"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    running_tasks: v.optional(v.array(v.id("running_tasks"))),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;

    // Verify the running workflow exists
    const runningWorkflow = await ctx.db.get(id);
    if (!runningWorkflow) throw new Error("Running workflow not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (runningWorkflow.userId !== userId) throw new Error("Not authorized");

    await ctx.db.patch(id, updates);
    return null;
  },
});

// Delete a running workflow
export const deleteRunningWorkflow = mutation({
  args: { id: v.id("running_workflows") },
  handler: async (ctx, args) => {
    // Verify the running workflow exists
    const runningWorkflow = await ctx.db.get(args.id);
    if (!runningWorkflow) throw new Error("Running workflow not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (runningWorkflow.userId !== userId) throw new Error("Not authorized");

    await ctx.db.delete(args.id);
    return true;
  },
});

// Add a running task to a running workflow
export const addRunningTaskToWorkflow = mutation({
  args: {
    workflowId: v.id("running_workflows"),
    taskId: v.id("running_tasks"),
  },
  handler: async (ctx, args) => {
    // Verify the running workflow exists
    const runningWorkflow = await ctx.db.get(args.workflowId);
    if (!runningWorkflow) throw new Error("Running workflow not found");

    // Verify the running task exists
    const runningTask = await ctx.db.get(args.taskId);
    if (!runningTask) throw new Error("Running task not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (runningWorkflow.userId !== userId) throw new Error("Not authorized");

    // Add running task to workflow if not already present
    const currentTasks = runningWorkflow.running_tasks || [];
    if (!currentTasks.includes(args.taskId)) {
      const updatedTasks = [...currentTasks, args.taskId];
      await ctx.db.patch(args.workflowId, { running_tasks: updatedTasks });
    }

    return null;
  },
});

// Remove a running task from a running workflow
export const removeRunningTaskFromWorkflow = mutation({
  args: {
    workflowId: v.id("running_workflows"),
    taskId: v.id("running_tasks"),
  },
  handler: async (ctx, args) => {
    // Verify the running workflow exists
    const runningWorkflow = await ctx.db.get(args.workflowId);
    if (!runningWorkflow) throw new Error("Running workflow not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (runningWorkflow.userId !== userId) throw new Error("Not authorized");

    // Remove running task from workflow
    const currentTasks = runningWorkflow.running_tasks || [];
    const updatedTasks = currentTasks.filter(taskId => taskId !== args.taskId);
    await ctx.db.patch(args.workflowId, { running_tasks: updatedTasks });

    return null;
  },
});

// Update the entire running tasks array for a running workflow
export const updateRunningWorkflowTasks = mutation({
  args: {
    workflowId: v.id("running_workflows"),
    running_tasks: v.array(v.id("running_tasks")),
  },
  handler: async (ctx, args) => {
    // Verify the running workflow exists
    const runningWorkflow = await ctx.db.get(args.workflowId);
    if (!runningWorkflow) throw new Error("Running workflow not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (runningWorkflow.userId !== userId) throw new Error("Not authorized");

    // Verify all running tasks exist
    const taskChecks = await Promise.all(
      args.running_tasks.map(taskId => ctx.db.get(taskId))
    );

    if (taskChecks.some(task => task === null)) {
      throw new Error("One or more running tasks not found");
    }

    // Update the running workflow with new tasks array
    await ctx.db.patch(args.workflowId, { running_tasks: args.running_tasks });

    return null;
  },
});

// Get a running workflow with populated running task details
export const getRunningWorkflowWithTasks = query({
  args: { id: v.id("running_workflows") },
  handler: async (ctx, args) => {
    const runningWorkflow = await ctx.db.get(args.id);
    if (!runningWorkflow) return null;

    // Get running task details if tasks exist
    if (runningWorkflow.running_tasks && runningWorkflow.running_tasks.length > 0) {
      const tasks = await Promise.all(
        runningWorkflow.running_tasks.map(taskId => ctx.db.get(taskId))
      );
      // Filter out any null tasks (in case a task was deleted)
      const validTasks = tasks.filter(task => task !== null);

      return {
        ...runningWorkflow,
        taskDetails: validTasks,
      };
    }

    return {
      ...runningWorkflow,
      taskDetails: [],
    };
  },
});

// Get all running tasks for a specific running workflow
export const getRunningTasksForWorkflow = query({
  args: { workflowId: v.id("running_workflows") },
  handler: async (ctx, args) => {
    const runningWorkflow = await ctx.db.get(args.workflowId);
    if (!runningWorkflow || !runningWorkflow.running_tasks) return [];

    // Get running task details
    const tasks = await Promise.all(
      runningWorkflow.running_tasks.map(taskId => ctx.db.get(taskId))
    );

    // Filter out any null tasks (in case a task was deleted)
    return tasks.filter(task => task !== null);
  },
});
