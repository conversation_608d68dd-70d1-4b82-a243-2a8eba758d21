"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { Id } from "../../../../convex/_generated/dataModel";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useForm } from "react-hook-form";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Trash2, Pencil } from "lucide-react"; // Import icons
import { useState } from "react";
import { Workflow } from "../../../../convex/workflows";
import { EditWorkflowDialog } from "./EditWorkflowDialog";

interface WorkflowFormData {
  title: string;
  description: string;
}

export default function WorkflowsPage() {
  const workflows = useQuery(api.workflows.getWorkflows);
  const createWorkflow = useMutation(api.workflows.createWorkflow);
  const updateWorkflow = useMutation(api.workflows.updateWorkflow);
  const deleteWorkflow = useMutation(api.workflows.deleteWorkflow);

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentWorkflow, setCurrentWorkflow] = useState<Workflow | null>(null);

  const form = useForm<WorkflowFormData>({
    defaultValues: {
      title: "",
      description: "",
    },
  });

  const onSubmit = async (data: WorkflowFormData) => {
    try {
      await createWorkflow({
        title: data.title,
        description: data.description || undefined,
      });
      form.reset();
    } catch (error) {
      console.error("Failed to create workflow:", error);
    }
  };

  // Handle workflow deletion
  const handleDeleteWorkflow = async (workflowId: Id<"workflows">) => {
    try {
      await deleteWorkflow({ id: workflowId });
    } catch (error) {
      console.error("Failed to delete workflow:", error);
    }
  };

  // Handle opening edit dialog
  const handleEditClick = (workflow: Workflow) => {
    setCurrentWorkflow(workflow);
    setIsEditDialogOpen(true);
  };

  // Handle workflow update
  const handleUpdateWorkflow = async (data: WorkflowFormData) => {
    if (!currentWorkflow) return;

    try {
      await updateWorkflow({
        id: currentWorkflow._id,
        title: data.title,
        description: data.description || undefined,
      });
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error("Failed to update workflow:", error);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <h1 className="text-3xl font-bold">Super Admin - Workflow Management</h1>

      {/* Create Workflow Form */}
      <Card>
        <CardHeader>
          <CardTitle>Create New Workflow</CardTitle>
          <CardDescription>Add a new workflow to the system</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="title"
                rules={{ required: "Title is required" }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter workflow title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Enter workflow description" className="min-h-[100px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" disabled={form.formState.isSubmitting}>
                {form.formState.isSubmitting ? "Creating..." : "Create Workflow"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Workflows Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Workflows</CardTitle>
          <CardDescription>View and manage existing workflows</CardDescription>
        </CardHeader>
        <CardContent>
          {workflows === undefined ? (
            <div>Loading workflows...</div>
          ) : workflows.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">No workflows found. Create your first workflow above.</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>User ID</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {workflows.map((workflow) => (
                  <TableRow key={workflow._id}>
                    <TableCell className="font-medium">{workflow.title}</TableCell>
                    <TableCell>{workflow.description || "No description"}</TableCell>
                    <TableCell className="font-mono text-sm">{workflow.userId}</TableCell>
                    <TableCell>{new Date(workflow.createdAt).toLocaleString()}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="icon" onClick={() => handleEditClick(workflow)} className="text-primary hover:bg-primary/10">
                          <Pencil className="size-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                        <Button variant="ghost" size="icon" onClick={() => handleDeleteWorkflow(workflow._id)} className="text-destructive hover:bg-destructive/10">
                          <Trash2 className="size-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <EditWorkflowDialog isOpen={isEditDialogOpen} onOpenChange={setIsEditDialogOpen} workflow={currentWorkflow} onSubmit={handleUpdateWorkflow} />
    </div>
  );
}
