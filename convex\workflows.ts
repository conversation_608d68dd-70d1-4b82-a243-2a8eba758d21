import { Id } from "./_generated/dataModel";
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export interface Workflow {
  _id: Id<"workflows">;
  title: string;
  description?: string;
  userId: string;
  createdAt: string;
  tasks?: Id<"tasks">[];
}

// Get all workflows
export const getWorkflows = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("workflows").collect();
  },
});

// Get a single workflow by ID
export const getWorkflow = query({
  args: { id: v.id("workflows") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Create a new workflow
export const createWorkflow = mutation({
  args: {
    title: v.string(),
    description: v.optional(v.string()),
    tasks: v.optional(v.array(v.id("tasks"))),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    const userId = user?.subject;
    if (!userId) throw new Error("Not authenticated");

    return await ctx.db.insert("workflows", {
      title: args.title,
      description: args.description,
      userId,
      createdAt: new Date().toISOString(),
      tasks: args.tasks ?? [],
    });
  },
});

// Update an existing workflow
export const updateWorkflow = mutation({
  args: {
    id: v.id("workflows"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    tasks: v.array(v.id("tasks")),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;

    // Verify the workflow exists
    const workflow = await ctx.db.get(id);
    if (!workflow) throw new Error("Workflow not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (workflow.userId !== userId) throw new Error("Not authorized");

    await ctx.db.patch(id, updates);
    return null;
  },
});

// Delete a workflow
export const deleteWorkflow = mutation({
  args: { id: v.id("workflows") },
  handler: async (ctx, args) => {
    // Verify the workflow exists
    const workflow = await ctx.db.get(args.id);
    if (!workflow) throw new Error("Workflow not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (workflow.userId !== userId) throw new Error("Not authorized");

    await ctx.db.delete(args.id);
    return true;
  },
});

// Add a task to a workflow
export const addTaskToWorkflow = mutation({
  args: {
    workflowId: v.id("workflows"),
    taskId: v.id("tasks"),
  },
  handler: async (ctx, args) => {
    // Verify the workflow exists
    const workflow = await ctx.db.get(args.workflowId);
    if (!workflow) throw new Error("Workflow not found");

    // Verify the task exists
    const task = await ctx.db.get(args.taskId);
    if (!task) throw new Error("Task not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (workflow.userId !== userId) throw new Error("Not authorized");

    // Add task to workflow if not already present
    const currentTasks = workflow.tasks || [];
    if (!currentTasks.includes(args.taskId)) {
      const updatedTasks = [...currentTasks, args.taskId];
      await ctx.db.patch(args.workflowId, { tasks: updatedTasks });
    }

    return null;
  },
});

// Remove a task from a workflow
export const removeTaskFromWorkflow = mutation({
  args: {
    workflowId: v.id("workflows"),
    taskId: v.id("tasks"),
  },
  handler: async (ctx, args) => {
    // Verify the workflow exists
    const workflow = await ctx.db.get(args.workflowId);
    if (!workflow) throw new Error("Workflow not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (workflow.userId !== userId) throw new Error("Not authorized");

    // Remove task from workflow
    const currentTasks = workflow.tasks || [];
    const updatedTasks = currentTasks.filter(taskId => taskId !== args.taskId);
    await ctx.db.patch(args.workflowId, { tasks: updatedTasks });

    return null;
  },
});

// Update the entire tasks array for a workflow (useful for reordering)
export const updateWorkflowTasks = mutation({
  args: {
    workflowId: v.id("workflows"),
    tasks: v.array(v.id("tasks")),
  },
  handler: async (ctx, args) => {
    // Verify the workflow exists
    const workflow = await ctx.db.get(args.workflowId);
    if (!workflow) throw new Error("Workflow not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (workflow.userId !== userId) throw new Error("Not authorized");

    // Verify all tasks exist
    const taskChecks = await Promise.all(
      args.tasks.map(taskId => ctx.db.get(taskId))
    );

    if (taskChecks.some(task => task === null)) {
      throw new Error("One or more tasks not found");
    }

    // Update the workflow with new tasks array
    await ctx.db.patch(args.workflowId, { tasks: args.tasks });

    return null;
  },
});

// Get a workflow with populated task details
export const getWorkflowWithTasks = query({
  args: { id: v.id("workflows") },
  handler: async (ctx, args) => {
    const workflow = await ctx.db.get(args.id);
    if (!workflow) return null;

    // Get task details if tasks exist
    if (workflow.tasks && workflow.tasks.length > 0) {
      const tasks = await Promise.all(
        workflow.tasks.map(taskId => ctx.db.get(taskId))
      );
      // Filter out any null tasks (in case a task was deleted)
      const validTasks = tasks.filter(task => task !== null);

      return {
        ...workflow,
        taskDetails: validTasks,
      };
    }

    return {
      ...workflow,
      taskDetails: [],
    };
  },
});

// Get all tasks for a specific workflow
export const getTasksForWorkflow = query({
  args: { workflowId: v.id("workflows") },
  handler: async (ctx, args) => {
    const workflow = await ctx.db.get(args.workflowId);
    if (!workflow || !workflow.tasks) return [];

    // Get task details
    const tasks = await Promise.all(
      workflow.tasks.map(taskId => ctx.db.get(taskId))
    );

    // Filter out any null tasks (in case a task was deleted)
    return tasks.filter(task => task !== null);
  },
});
