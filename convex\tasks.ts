// import { useUser } from "@clerk/nextjs";
import { Id } from "./_generated/dataModel";
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export interface Task {
  _id: Id<"tasks">;
  title: string;
  description?: string;
  userId: string;
  createdAt: string;
}

// Get all tasks
export const getTasks = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("tasks").collect();
  },
});

// Get a single task by ID
export const getTask = query({
  args: { id: v.id("tasks") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Create a new task
export const createTask = mutation({
  args: {
    title: v.string(),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    const userId = user?.subject;
    if (!userId) throw new Error("Not authenticated");

    return await ctx.db.insert("tasks", {
      title: args.title,
      description: args.description,
      userId,
      createdAt: new Date().toISOString(),
    });
  },
});

// Update an existing task
export const updateTask = mutation({
  args: {
    id: v.id("tasks"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;

    // Verify the task exists
    const task = await ctx.db.get(id);
    if (!task) throw new Error("Task not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (task.userId !== userId) throw new Error("Not authorized");

    return await ctx.db.patch(id, updates);
  },
});

// Delete a task
export const deleteTask = mutation({
  args: { id: v.id("tasks") },
  handler: async (ctx, args) => {
    // Verify the task exists
    const task = await ctx.db.get(args.id);
    if (!task) throw new Error("Task not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (task.userId !== userId) throw new Error("Not authorized");

    await ctx.db.delete(args.id);
    return true;
  },
});