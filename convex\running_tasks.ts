import { Id } from "./_generated/dataModel";
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export interface RunningTask {
  _id: Id<"running_tasks">;
  title: string;
  description?: string;
  userId: string;
  createdAt: string;
  status: "Not Started" | "Running" | "Complete";
}

// Status enum for validation
export const TaskStatus = {
  NOT_STARTED: "Not Started" as const,
  RUNNING: "Running" as const,
  COMPLETE: "Complete" as const,
} as const;

// Get all running tasks
export const getRunningTasks = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("running_tasks").collect();
  },
});

// Get a single running task by ID
export const getRunningTask = query({
  args: { id: v.id("running_tasks") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Get running tasks by status
export const getRunningTasksByStatus = query({
  args: { 
    status: v.union(v.literal("Not Started"), v.literal("Running"), v.literal("Complete"))
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("running_tasks")
      .filter((q) => q.eq(q.field("status"), args.status))
      .collect();
  },
});

// Get running tasks by user
export const getRunningTasksByUser = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("running_tasks")
      .withIndex("byUserId", (q) => q.eq("userId", args.userId))
      .collect();
  },
});

// Create a new running task
export const createRunningTask = mutation({
  args: {
    title: v.string(),
    description: v.optional(v.string()),
    status: v.optional(v.union(v.literal("Not Started"), v.literal("Running"), v.literal("Complete"))),
  },
  handler: async (ctx, args) => {
    const user = await ctx.auth.getUserIdentity();
    const userId = user?.subject;
    if (!userId) throw new Error("Not authenticated");

    return await ctx.db.insert("running_tasks", {
      title: args.title,
      description: args.description,
      userId,
      createdAt: new Date().toISOString(),
      status: args.status ?? TaskStatus.NOT_STARTED, // Default to "Not Started"
    });
  },
});

// Update an existing running task
export const updateRunningTask = mutation({
  args: {
    id: v.id("running_tasks"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    status: v.optional(v.union(v.literal("Not Started"), v.literal("Running"), v.literal("Complete"))),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;

    // Verify the running task exists
    const runningTask = await ctx.db.get(id);
    if (!runningTask) throw new Error("Running task not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (runningTask.userId !== userId) throw new Error("Not authorized");

    return await ctx.db.patch(id, updates);
  },
});

// Update only the status of a running task
export const updateRunningTaskStatus = mutation({
  args: {
    id: v.id("running_tasks"),
    status: v.union(v.literal("Not Started"), v.literal("Running"), v.literal("Complete")),
  },
  handler: async (ctx, args) => {
    // Verify the running task exists
    const runningTask = await ctx.db.get(args.id);
    if (!runningTask) throw new Error("Running task not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (runningTask.userId !== userId) throw new Error("Not authorized");

    await ctx.db.patch(args.id, { status: args.status });
    return null;
  },
});

// Delete a running task
export const deleteRunningTask = mutation({
  args: { id: v.id("running_tasks") },
  handler: async (ctx, args) => {
    // Verify the running task exists
    const runningTask = await ctx.db.get(args.id);
    if (!runningTask) throw new Error("Running task not found");

    // Verify ownership
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");
    if (runningTask.userId !== userId) throw new Error("Not authorized");

    await ctx.db.delete(args.id);
    return true;
  },
});

// Bulk update status for multiple running tasks
export const bulkUpdateRunningTaskStatus = mutation({
  args: {
    taskIds: v.array(v.id("running_tasks")),
    status: v.union(v.literal("Not Started"), v.literal("Running"), v.literal("Complete")),
  },
  handler: async (ctx, args) => {
    const userId = (await ctx.auth.getUserIdentity())?.subject;
    if (!userId) throw new Error("Not authenticated");

    // Verify all tasks exist and user owns them
    const tasks = await Promise.all(
      args.taskIds.map(taskId => ctx.db.get(taskId))
    );

    for (const task of tasks) {
      if (!task) throw new Error("One or more running tasks not found");
      if (task.userId !== userId) throw new Error("Not authorized to update one or more tasks");
    }

    // Update all tasks
    await Promise.all(
      args.taskIds.map(taskId => 
        ctx.db.patch(taskId, { status: args.status })
      )
    );

    return null;
  },
});
