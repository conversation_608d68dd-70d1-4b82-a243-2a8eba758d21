# VS Code Debugging Guide

This project is configured with comprehensive VS Code debugging support for Next.js and Convex.

## 🚀 Quick Start

1. **Open the Debug Panel**: Press `Ctrl+Shift+D` (Windows/Linux) or `Cmd+Shift+D` (Mac)
2. **Select a debug configuration** from the dropdown
3. **Press F5** or click the green play button to start debugging

## 🔧 Available Debug Configurations

### 1. **Next.js: debug server-side**
- Debugs Next.js server-side code (API routes, server components)
- Set breakpoints in `/src/app/api/` files or server components
- Automatically starts Next.js dev server with debugging enabled

### 2. **Next.js: debug client-side**
- Debugs React components and client-side code
- Opens Chrome with debugging enabled
- Set breakpoints in React components, hooks, etc.

### 3. **Next.js: debug full stack**
- Debugs both server and client-side code simultaneously
- Best for full-stack debugging scenarios
- Automatically opens browser when server is ready

### 4. **Convex: debug functions**
- Debugs Convex backend functions (queries, mutations, actions)
- Set breakpoints in `/convex/` files
- Runs `convex dev` with debugging enabled

### 5. **Debug: Next.js + Convex** (Compound)
- Runs both Next.js and Convex debugging simultaneously
- Perfect for debugging the entire stack
- **Recommended for most debugging scenarios**

### 6. **Debug: Current TypeScript File**
- Debugs the currently open TypeScript file
- Useful for testing individual functions or utilities

## 🎯 How to Set Breakpoints

1. **Open the file** you want to debug
2. **Click in the gutter** (left of line numbers) to set a breakpoint
3. **Red dot** indicates an active breakpoint
4. **Start debugging** with one of the configurations above

## 📁 Where to Set Breakpoints

### Frontend (React/Next.js)
```
src/app/                    # App Router pages
src/components/             # React components
src/hooks/                  # Custom hooks
src/lib/                    # Utility functions
```

### Backend (Convex)
```
convex/tasks.ts            # Task queries/mutations
convex/users.tsx           # User queries/mutations
convex/http.ts             # HTTP actions (webhooks)
```

### API Routes
```
src/app/api/               # Next.js API routes
```

## 🔍 Debugging Examples

### Debug Task Creation
1. Set breakpoint in `src/app/superadmin/page.tsx` in the `onSubmit` function
2. Set breakpoint in `convex/tasks.ts` in the `createTask` mutation
3. Use **"Debug: Next.js + Convex"** configuration
4. Fill out the task form and submit

### Debug Authentication
1. Set breakpoint in `convex/users.tsx` in the `getCurrentUser` function
2. Set breakpoint in any component using `useQuery(api.users.current)`
3. Use **"Debug: Next.js + Convex"** configuration

### Debug API Routes
1. Set breakpoint in any file under `src/app/api/`
2. Use **"Next.js: debug server-side"** configuration
3. Make HTTP request to your API endpoint

## 🛠️ Debugging Tools

### Debug Console
- Access variables and execute code in the current scope
- Available in VS Code Debug Console panel

### Call Stack
- See the execution path that led to the current breakpoint
- Navigate up and down the call stack

### Variables Panel
- Inspect local and global variables
- Expand objects to see their properties

### Watch Panel
- Add expressions to watch their values change
- Useful for monitoring specific variables

## ⚡ Pro Tips

1. **Use conditional breakpoints**: Right-click on a breakpoint to add conditions
2. **Log points**: Add console.log without modifying code (right-click breakpoint)
3. **Step through code**: Use F10 (step over), F11 (step into), Shift+F11 (step out)
4. **Hot reload**: Code changes are reflected without restarting the debugger
5. **Multiple terminals**: Each debug configuration runs in its own terminal

## 🚨 Troubleshooting

### Breakpoints not hitting?
- Ensure the correct debug configuration is selected
- Check that source maps are enabled
- Verify the file path matches the running code

### Port conflicts?
- Next.js runs on port 3000
- Convex debug runs on port 9230
- Chrome debugging uses a random port

### Environment variables not loaded?
- Restart the debug session after changing `.env.local`
- Check that all required environment variables are set

## 📚 Additional Resources

- [VS Code Debugging Guide](https://code.visualstudio.com/docs/editor/debugging)
- [Next.js Debugging](https://nextjs.org/docs/advanced-features/debugging)
- [Convex Debugging](https://docs.convex.dev/debugging)
