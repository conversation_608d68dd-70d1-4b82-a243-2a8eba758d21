"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { Id } from "../../../../convex/_generated/dataModel";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, Users, Workflow as WorkflowIcon } from "lucide-react";
import { useState } from "react";
import { Workflow } from "../../../../convex/workflows";
import { Task } from "../../../../convex/tasks";
import { AddTaskToWorkflowDialog } from "./AddTaskToWorkflowDialog";

export default function WorkflowTasksPage() {
  const workflows = useQuery(api.workflows.getWorkflows);
  const tasks = useQuery(api.tasks.getTasks);
  const addTaskToWorkflow = useMutation(api.workflows.addTaskToWorkflow);
  const removeTaskFromWorkflow = useMutation(api.workflows.removeTaskFromWorkflow);

  const [selectedWorkflowId, setSelectedWorkflowId] = useState<Id<"workflows"> | null>(null);
  const [isAddTaskDialogOpen, setIsAddTaskDialogOpen] = useState(false);

  const getWorkflowWithTasks = useQuery(api.workflows.getWorkflowWithTasks, selectedWorkflowId ? { id: selectedWorkflowId } : "skip");

  const handleRemoveTaskFromWorkflow = async (taskId: Id<"tasks">) => {
    if (!selectedWorkflowId) return;

    try {
      await removeTaskFromWorkflow({
        workflowId: selectedWorkflowId,
        taskId,
      });
    } catch (error) {
      console.error("Failed to remove task from workflow:", error);
    }
  };

  const handleAddTaskToWorkflow = async (taskId: Id<"tasks">) => {
    if (!selectedWorkflowId) return;

    try {
      await addTaskToWorkflow({
        workflowId: selectedWorkflowId,
        taskId,
      });
    } catch (error) {
      console.error("Failed to add task to workflow:", error);
    }
  };

  const selectedWorkflow = workflows?.find((w) => w._id === selectedWorkflowId);
  const workflowWithTasks = getWorkflowWithTasks;

  // Get tasks that are not already in the selected workflow
  const availableTasks = tasks?.filter((task) => !workflowWithTasks?.taskDetails?.some((wt) => wt._id === task._id)) || [];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Workflow Tasks Management</h1>
          <p className="text-muted-foreground">Manage the association between workflows and tasks</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Workflows List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <WorkflowIcon className="h-5 w-5" />
              Workflows
            </CardTitle>
            <CardDescription>Select a workflow to manage its tasks</CardDescription>
          </CardHeader>
          <CardContent>
            {workflows === undefined ? (
              <div className="text-center py-4">Loading workflows...</div>
            ) : workflows.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">No workflows found</div>
            ) : (
              <div className="space-y-2">
                {workflows.map((workflow) => (
                  <div
                    key={workflow._id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${selectedWorkflowId === workflow._id ? "bg-primary/10 border-primary" : "hover:bg-muted"}`}
                    onClick={() => setSelectedWorkflowId(workflow._id)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">{workflow.title}</h3>
                        {workflow.description && <p className="text-sm text-muted-foreground">{workflow.description}</p>}
                      </div>
                      <Badge variant="secondary">{workflow.tasks?.length || 0} tasks</Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tasks Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Tasks in Workflow
            </CardTitle>
            <CardDescription>{selectedWorkflow ? `Managing tasks for "${selectedWorkflow.title}"` : "Select a workflow to manage its tasks"}</CardDescription>
          </CardHeader>
          <CardContent>
            {!selectedWorkflowId ? (
              <div className="text-center py-8 text-muted-foreground">Select a workflow from the left to manage its tasks</div>
            ) : workflowWithTasks === undefined ? (
              <div className="text-center py-4">Loading tasks...</div>
            ) : (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium">Tasks ({workflowWithTasks.taskDetails?.length || 0})</h3>
                  <Button onClick={() => setIsAddTaskDialogOpen(true)} size="sm" disabled={availableTasks.length === 0}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Task
                  </Button>
                </div>

                {workflowWithTasks.taskDetails && workflowWithTasks.taskDetails.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Title</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead className="w-[100px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {workflowWithTasks.taskDetails.map((task) => (
                        <TableRow key={task._id}>
                          <TableCell className="font-medium">{task.title}</TableCell>
                          <TableCell className="text-muted-foreground">{task.description || "No description"}</TableCell>
                          <TableCell>
                            <Button variant="outline" size="sm" onClick={() => handleRemoveTaskFromWorkflow(task._id)}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">No tasks assigned to this workflow</div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Add Task Dialog */}
      <AddTaskToWorkflowDialog
        isOpen={isAddTaskDialogOpen}
        onOpenChange={setIsAddTaskDialogOpen}
        availableTasks={availableTasks}
        onAddTask={handleAddTaskToWorkflow}
        workflowTitle={selectedWorkflow?.title || ""}
      />
    </div>
  );
}
