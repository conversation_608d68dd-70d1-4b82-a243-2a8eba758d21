"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { Id } from "../../../../convex/_generated/dataModel";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Trash2, Play, Pause, CheckCircle, Workflow as WorkflowIcon } from "lucide-react";
import { useState } from "react";

interface WorkflowSelectionFormData {
  workflowId: string;
}

export default function RunningTasksPage() {
  const workflows = useQuery(api.workflows.getWorkflows);
  const runningTasks = useQuery(api.running_tasks.getRunningTasks);
  const updateRunningTaskStatus = useMutation(api.running_tasks.updateRunningTaskStatus);
  const deleteRunningTask = useMutation(api.running_tasks.deleteRunningTask);

  const [isCreating, setIsCreating] = useState(false);

  const form = useForm<WorkflowSelectionFormData>({
    defaultValues: {
      workflowId: "",
    },
  });

  const createRunningWorkflowFromWorkflow = useMutation(api.running_workflows.createRunningWorkflowFromWorkflow);

  const onSubmit = async (data: WorkflowSelectionFormData) => {
    if (!data.workflowId) return;

    setIsCreating(true);
    try {
      await createRunningWorkflowFromWorkflow({
        workflowId: data.workflowId as Id<"workflows">,
      });
      form.reset();
    } catch (error) {
      console.error("Failed to create running workflow from workflow:", error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleStatusChange = async (taskId: Id<"running_tasks">, newStatus: "Not Started" | "Running" | "Complete") => {
    try {
      await updateRunningTaskStatus({
        id: taskId,
        status: newStatus,
      });
    } catch (error) {
      console.error("Failed to update task status:", error);
    }
  };

  const handleDelete = async (taskId: Id<"running_tasks">) => {
    try {
      await deleteRunningTask({ id: taskId });
    } catch (error) {
      console.error("Failed to delete running task:", error);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Not Started":
        return "secondary";
      case "Running":
        return "default";
      case "Complete":
        return "outline";
      default:
        return "secondary";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Not Started":
        return <Pause className="h-4 w-4" />;
      case "Running":
        return <Play className="h-4 w-4" />;
      case "Complete":
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Pause className="h-4 w-4" />;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Running Workflows & Tasks</h1>
          <p className="text-muted-foreground">Create running workflows from existing workflows and manage running task statuses</p>
        </div>
      </div>

      {/* Create Running Workflow from Workflow */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <WorkflowIcon className="h-5 w-5" />
            Create Running Workflow from Existing Workflow
          </CardTitle>
          <CardDescription>Select an existing workflow to create a running workflow with all tasks copied as running tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="workflowId"
                rules={{ required: "Please select a workflow" }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Select Workflow</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose a workflow to copy..." />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {workflows?.map((workflow) => (
                          <SelectItem key={workflow._id} value={workflow._id}>
                            <div className="flex items-center justify-between w-full">
                              <span>{workflow.title}</span>
                              <Badge variant="secondary" className="ml-2">
                                {workflow.tasks?.length || 0} tasks
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" disabled={isCreating || !workflows || workflows.length === 0}>
                {isCreating ? "Creating Running Workflow..." : "Create Running Workflow"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Running Tasks Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Running Tasks</CardTitle>
          <CardDescription>View and manage existing running tasks with status updates</CardDescription>
        </CardHeader>
        <CardContent>
          {runningTasks === undefined ? (
            <div>Loading running tasks...</div>
          ) : runningTasks.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">No running tasks found. Create your first running task above.</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {runningTasks.map((task) => (
                  <TableRow key={task._id}>
                    <TableCell className="font-medium">{task.title}</TableCell>
                    <TableCell>{task.description || "No description"}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Badge variant={getStatusBadgeVariant(task.status)} className="flex items-center gap-1">
                          {getStatusIcon(task.status)}
                          {task.status}
                        </Badge>
                        <Select value={task.status} onValueChange={(value) => handleStatusChange(task._id, value as "Not Started" | "Running" | "Complete")}>
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Not Started">Not Started</SelectItem>
                            <SelectItem value="Running">Running</SelectItem>
                            <SelectItem value="Complete">Complete</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </TableCell>
                    <TableCell>{new Date(task.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm" onClick={() => handleDelete(task._id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
