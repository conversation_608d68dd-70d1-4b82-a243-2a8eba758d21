"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { Id } from "../../../../convex/_generated/dataModel";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Trash2, <PERSON><PERSON><PERSON>, <PERSON>, Pause, CheckCircle } from "lucide-react";
import { useState } from "react";
import { RunningTask } from "../../../../convex/running_tasks";

interface RunningTaskFormData {
  title: string;
  description: string;
  status: "Not Started" | "Running" | "Complete";
}

export default function RunningTasksPage() {
  const runningTasks = useQuery(api.running_tasks.getRunningTasks);
  const createRunningTask = useMutation(api.running_tasks.createRunningTask);
  const updateRunningTask = useMutation(api.running_tasks.updateRunningTask);
  const updateRunningTaskStatus = useMutation(api.running_tasks.updateRunningTaskStatus);
  const deleteRunningTask = useMutation(api.running_tasks.deleteRunningTask);

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentTask, setCurrentTask] = useState<RunningTask | null>(null);

  const form = useForm<RunningTaskFormData>({
    defaultValues: {
      title: "",
      description: "",
      status: "Not Started",
    },
  });

  const onSubmit = async (data: RunningTaskFormData) => {
    try {
      await createRunningTask({
        title: data.title,
        description: data.description || undefined,
        status: data.status,
      });
      form.reset();
    } catch (error) {
      console.error("Failed to create running task:", error);
    }
  };

  const handleStatusChange = async (taskId: Id<"running_tasks">, newStatus: "Not Started" | "Running" | "Complete") => {
    try {
      await updateRunningTaskStatus({
        id: taskId,
        status: newStatus,
      });
    } catch (error) {
      console.error("Failed to update task status:", error);
    }
  };

  const handleDelete = async (taskId: Id<"running_tasks">) => {
    try {
      await deleteRunningTask({ id: taskId });
    } catch (error) {
      console.error("Failed to delete running task:", error);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Not Started":
        return "secondary";
      case "Running":
        return "default";
      case "Complete":
        return "outline";
      default:
        return "secondary";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Not Started":
        return <Pause className="h-4 w-4" />;
      case "Running":
        return <Play className="h-4 w-4" />;
      case "Complete":
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Pause className="h-4 w-4" />;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Running Tasks</h1>
          <p className="text-muted-foreground">
            Manage running tasks with status tracking
          </p>
        </div>
      </div>

      {/* Create Running Task Form */}
      <Card>
        <CardHeader>
          <CardTitle>Create New Running Task</CardTitle>
          <CardDescription>Add a new running task with status tracking</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="title"
                  rules={{ required: "Title is required" }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter running task title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Not Started">Not Started</SelectItem>
                          <SelectItem value="Running">Running</SelectItem>
                          <SelectItem value="Complete">Complete</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Enter running task description" className="min-h-[100px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" disabled={form.formState.isSubmitting}>
                {form.formState.isSubmitting ? "Creating..." : "Create Running Task"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Running Tasks Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Running Tasks</CardTitle>
          <CardDescription>View and manage existing running tasks with status updates</CardDescription>
        </CardHeader>
        <CardContent>
          {runningTasks === undefined ? (
            <div>Loading running tasks...</div>
          ) : runningTasks.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No running tasks found. Create your first running task above.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {runningTasks.map((task) => (
                  <TableRow key={task._id}>
                    <TableCell className="font-medium">{task.title}</TableCell>
                    <TableCell>{task.description || "No description"}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Badge variant={getStatusBadgeVariant(task.status)} className="flex items-center gap-1">
                          {getStatusIcon(task.status)}
                          {task.status}
                        </Badge>
                        <Select
                          value={task.status}
                          onValueChange={(value) => handleStatusChange(task._id, value as "Not Started" | "Running" | "Complete")}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Not Started">Not Started</SelectItem>
                            <SelectItem value="Running">Running</SelectItem>
                            <SelectItem value="Complete">Complete</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </TableCell>
                    <TableCell>{new Date(task.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(task._id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
