"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Search, Plus } from "lucide-react";
import { Id } from "../../../../convex/_generated/dataModel";
import { Task } from "../../../../convex/tasks";

interface AddTaskToWorkflowDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  availableTasks: Task[];
  onAddTask: (taskId: Id<"tasks">) => Promise<void>;
  workflowTitle: string;
}

export function AddTaskToWorkflowDialog({ 
  isOpen, 
  onOpenChange, 
  availableTasks, 
  onAddTask, 
  workflowTitle 
}: AddTaskToWorkflowDialogProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [isAdding, setIsAdding] = useState<Id<"tasks"> | null>(null);

  // Filter tasks based on search term
  const filteredTasks = availableTasks.filter(task =>
    task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (task.description && task.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleAddTask = async (taskId: Id<"tasks">) => {
    setIsAdding(taskId);
    try {
      await onAddTask(taskId);
      // Don't close the dialog automatically - let user add multiple tasks
    } catch (error) {
      console.error("Failed to add task:", error);
    } finally {
      setIsAdding(null);
    }
  };

  const handleClose = () => {
    setSearchTerm("");
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Add Tasks to Workflow</DialogTitle>
          <DialogDescription>
            Add tasks to "{workflowTitle}". You can add multiple tasks at once.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 flex flex-col space-y-4 min-h-0">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Tasks List */}
          <div className="flex-1 overflow-auto border rounded-md">
            {filteredTasks.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {availableTasks.length === 0 
                  ? "All tasks are already assigned to this workflow"
                  : "No tasks found matching your search"
                }
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="w-[100px]">Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTasks.map((task) => (
                    <TableRow key={task._id}>
                      <TableCell className="font-medium">{task.title}</TableCell>
                      <TableCell className="text-muted-foreground">
                        {task.description || "No description"}
                      </TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          onClick={() => handleAddTask(task._id)}
                          disabled={isAdding === task._id}
                        >
                          {isAdding === task._id ? (
                            "Adding..."
                          ) : (
                            <>
                              <Plus className="h-4 w-4 mr-1" />
                              Add
                            </>
                          )}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Done
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
