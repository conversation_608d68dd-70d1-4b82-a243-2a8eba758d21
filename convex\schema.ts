import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
    users: defineTable({
        name: v.string(),
        firstName: v.optional(v.string()),
        lastName: v.optional(v.string()),
        imageUrl: v.optional(v.string()),
        clerkUserId: v.string(),
        email: v.string(),
    }).index("byClerkUserId", ["clerkUserId"]),

    tasks: defineTable({
        title: v.string(),
        description: v.optional(v.string()),
        userId: v.string(),
        createdAt: v.string(),
    }).index("byUserId", ["userId"]),

    workflows: defineTable({
        title: v.string(),
        description: v.optional(v.string()),
        userId: v.string(),
        createdAt: v.string(),
        tasks: v.array(v.id("tasks")),
    }).index("byUserId", ["userId"]),

    running_workflows: defineTable({
        title: v.string(),
        description: v.optional(v.string()),
        userId: v.string(),
        createdAt: v.string(),
        running_tasks: v.array(v.id("running_tasks")),
    }).index("byUserId", ["userId"]),

    running_tasks: defineTable({
        title: v.string(),
        description: v.optional(v.string()),
        userId: v.string(),
        createdAt: v.string(),
        status: v.union(v.literal("Not Started"), v.literal("Running"), v.literal("Complete")),
    }).index("byUserId", ["userId"]),
});
